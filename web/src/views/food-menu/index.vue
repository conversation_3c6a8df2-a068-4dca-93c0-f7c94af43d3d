<script setup>
import { h, onMounted, ref, resolveDirective, withDirectives } from 'vue'
import {
  NButton,
  NForm,
  NFormItem,
  NInputNumber,
  NPopconfirm,
  NTag,
  NSelect,
  NSpace,
  NDataTable,
  NModal,
  NCard,
  NStatistic,
  NDatePicker,
  NInput,
  NCollapse,
  NCollapseItem,
  useMessage
} from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudModal from '@/components/table/CrudModal.vue'
import CrudTable from '@/components/table/CrudTable.vue'

import { renderIcon } from '@/utils'
import { useCRUD } from '@/composables'
import api from '@/api'
import TheIcon from '@/components/icon/TheIcon.vue'

defineOptions({ name: '食谱管理' })

const $message = useMessage()
const $table = ref(null)
const queryItems = ref({})
const vPermission = resolveDirective('permission')

// 下单相关变量
const showOrderModal = ref(false)
const currentMenu = ref(null)
const totalStudentCount = ref(0)
const foodStuffStoreData = ref([])
const orderLoading = ref(false)
const orderQuantities = ref({})

// 查看详情相关
const showDetailModal = ref(false)
const currentRecipeDetail = ref(null)

// 打开下单弹窗 - 支持新的食谱数据结构
async function openOrderModal(row) {
  orderLoading.value = true
  showOrderModal.value = true
  currentMenu.value = row

  try {
    // 获取学生总数
    const studentCountRes = await api.getTotalStudentCount()
    if (studentCountRes && studentCountRes.data) {
      totalStudentCount.value = studentCountRes.data.total_count
    }

    // 从菜品列表中提取所有食材
    const allIngredients = []

    // 处理食谱数据结构（包含多个餐次）
    if (row.meals && row.meals.length > 0) {
      row.meals.forEach(meal => {
        if (meal.dish_list && meal.dish_list.length > 0) {
          meal.dish_list.forEach(dish => {
            if (dish.ingredients && dish.ingredients.length > 0) {
              dish.ingredients.forEach(ingredient => {
                const existingIngredient = allIngredients.find(item => item.food_stuff_id === ingredient.food_stuff_id)
                if (existingIngredient) {
                  // 如果食材已存在，累加数量
                  existingIngredient.quantity += ingredient.quantity
                } else {
                  // 如果食材不存在，添加新的食材
                  allIngredients.push({
                    food_stuff_id: ingredient.food_stuff_id,
                    food_stuff_name: ingredient.food_stuff_name,
                    food_stuff_unit: ingredient.unit_name,
                    food_stuff_count: ingredient.quantity
                  })
                }
              })
            }
          })
        }
      })
    }

    // 获取食材库存
    if (allIngredients.length > 0) {
      const foodStuffIds = allIngredients.map(item => item.food_stuff_id)
      const storeRes = await api.getFoodStuffStoreList({
        food_stuff_ids: foodStuffIds.join(',')
      })

      if (storeRes && storeRes.data) {
        foodStuffStoreData.value = storeRes.data.items

        // 初始化下单数量
        orderQuantities.value = {}
        allIngredients.forEach(item => {
          const required = calculateRequiredAmount(item)
          const store = getStoreAmount(item.food_stuff_id)
          // 计算初始下单数量：需要数量减去库存数量，最小为0
          const initialOrderQuantity = Math.max(0, required - store)
          orderQuantities.value[item.food_stuff_id] = initialOrderQuantity
        })
      }
    }


    // 将合并后的食材列表存储到currentMenu中，用于下单弹窗显示
    currentMenu.value.food_stuff_list = allIngredients

  } catch (error) {
    console.error('获取数据失败:', error)
    $message.error('获取数据失败')
  } finally {
    orderLoading.value = false
  }
}

// 关闭下单弹窗
function closeOrderModal() {
  showOrderModal.value = false
  currentMenu.value = null
}

// 计算需要的食材数量
function calculateRequiredAmount(foodStuffItem) {
  return foodStuffItem.food_stuff_count * totalStudentCount.value
}

// 获取食材库存数量
function getStoreAmount(foodStuffId) {
  const storeItem = foodStuffStoreData.value.find(item => item.food_stuff_id === foodStuffId)
  return storeItem ? storeItem.store_count : 0
}

// 提交订单
async function submitOrder() {
  try {
    // 准备订单数据
    const orderItems = currentMenu.value.food_stuff_list.map(item => {
      // 使用用户设置的下单数量
      const orderQuantity = orderQuantities.value[item.food_stuff_id] || 0
      return {
        id: item.food_stuff_id,
        name: item.food_stuff_name,
        quantity: orderQuantity,
        unitName: item.food_stuff_unit
      }
    }).filter(item => item.quantity > 0) // 只包含数量大于0的项目

    // 如果没有需要下单的食材，提示用户
    if (orderItems.length === 0) {
      $message.warning('没有需要下单的食材')
      return
    }

    // 生成订单备注
    const mealNames = currentMenu.value.meals.map(meal =>
      mealTypeOptions.find(item => item.value === meal.meal_type)?.label || `餐次${meal.meal_type}`
    ).join('、')
    const orderRemark = `根据食谱 ${formatDateForDisplay(currentMenu.value.recipe_date)} ${mealNames} 自动下单`

    const orderData = {
      orderItems: orderItems,
      orderRemark: orderRemark
    }

    // 调用下单API
    await api.createOrder(orderData)

    // 关闭弹窗
    showOrderModal.value = false
    // 显示成功提示
    $message.success('下单成功')
  } catch (error) {
    console.error('下单失败:', error)
    $message.error('下单失败，请重试')
  }
}


// 日期格式化函数
function formatDateForDisplay(dateValue) {
  if (!dateValue) return ''
  const date = new Date(dateValue)
  return date.toLocaleDateString('zh-CN')
}

// 餐次类型选项
const mealTypeOptions = [
  { label: '早餐', value: 1 },
  { label: '上午加餐', value: 2 },
  { label: '午餐', value: 3 },
  { label: '下午加餐', value: 4 },
  { label: '晚餐', value: 5 }
]

// 菜品列表
const dishOptions = ref([])

// 获取菜品列表
async function fetchDishOptions() {
  try {
    const res = await api.getDishList({ size: 9999 })
    if (res && res.data) {
      dishOptions.value = res.data.map(item => ({
        label: item.dish_name,
        value: item.id,
        ingredients: item.ingredients || []
      }))
    }
  } catch (error) {
    console.error('加载菜品数据失败:', error)
  }
}

// 添加餐次
function addMeal() {
  modalForm.value.meals.push({
    meal_type: 1,
    dish_list: [],
    is_active: true
  })
}

// 移除餐次
function removeMeal(index) {
  if (modalForm.value.meals && modalForm.value.meals.length > index) {
    modalForm.value.meals.splice(index, 1)
  }
}

// 添加菜品到餐次
function addDishToMeal(mealIndex) {
  if (!modalForm.value.meals[mealIndex].dish_list) {
    modalForm.value.meals[mealIndex].dish_list = []
  }
  modalForm.value.meals[mealIndex].dish_list.push({
    dish_id: null,
    dish_name: '',
    ingredients: []
  })
}

// 移除餐次中的菜品
function removeDishFromMeal(mealIndex, dishIndex) {
  if (modalForm.value.meals[mealIndex].dish_list &&
      modalForm.value.meals[mealIndex].dish_list.length > dishIndex) {
    modalForm.value.meals[mealIndex].dish_list.splice(dishIndex, 1)
  }
}

// 菜品选择变更时更新菜品信息
function updateDishInfo(mealIndex, dishIndex, dishId) {
  if (!modalForm.value.meals[mealIndex].dish_list) return

  const selectedDish = dishOptions.value.find(item => item.value === dishId)
  if (selectedDish) {
    const newItem = {
      dish_id: dishId,
      dish_name: selectedDish.label,
      ingredients: selectedDish.ingredients || []
    }
    modalForm.value.meals[mealIndex].dish_list.splice(dishIndex, 1, newItem)
  }
}

// 获取餐次类型名称
function getMealTypeName(mealType) {
  const option = mealTypeOptions.find(item => item.value === mealType)
  return option ? option.label : `未知餐次(${mealType})`
}

// 查看详情
async function handleViewDetail(row) {
  try {
    const res = await api.getRecipeById({ id: row.id })
    if (res && res.data) {
      currentRecipeDetail.value = res.data
      showDetailModal.value = true
    }
  } catch (error) {
    console.error('获取食谱详情失败:', error)
    $message.error('获取食谱详情失败')
  }
}

// 关闭详情弹窗
function closeDetailModal() {
  showDetailModal.value = false
  currentRecipeDetail.value = null
}

const {
  modalVisible,
  modalAction,
  modalTitle,
  modalLoading,
  handleAdd: originalHandleAdd,
  handleDelete,
  handleEdit: originalHandleEdit,
  handleSave,
  modalForm,
  modalFormRef,
} = useCRUD({
  name: '食谱',
  initForm: {
    recipeDate: null,
    recipeName: '',
    isActive: true,
    meals: []
  },
  doCreate: api.createRecipe,
  doDelete: api.deleteRecipe,
  doUpdate: api.updateRecipe,
  refresh: () => $table.value?.handleSearch(),
})

// 重写handleAdd方法
const handleAdd = async () => {
  await fetchDishOptions()
  // 初始化一个餐次
  modalForm.value.meals = [{
    meal_type: 1,
    dish_list: [],
    is_active: true
  }]
  originalHandleAdd()
}

// 重写handleEdit方法
const handleEdit = async (row) => {
  await fetchDishOptions()
  const formData = {
    id: row.id,
    recipeDate: row.recipe_date ? new Date(row.recipe_date).getTime() : null,
    recipeName: row.recipe_name || '',
    isActive: row.is_active,
    meals: row.meals || []
  }
  originalHandleEdit(formData)
}

// 表格列定义
const columns = [
  {
    title: '日期',
    key: 'recipe_date',
    width: 120,
    align: 'center',
    render(row) {
      return row.recipe_date ? formatDateForDisplay(row.recipe_date) : '-'
    }
  },
  {
    title: '食谱名称',
    key: 'recipe_name',
    width: 150,
    align: 'center',
    render(row) {
      return row.recipe_name || '-'
    }
  },
  {
    title: '餐次数量',
    key: 'meal_count',
    width: 100,
    align: 'center',
    render(row) {
      return row.meals ? row.meals.length : 0
    }
  },
  {
    title: '餐次信息',
    key: 'meals',
    align: 'center',
    render(row) {
      if (!row.meals || row.meals.length === 0) {
        return h('span', '无餐次信息')
      }

      return h(
        NSpace,
        { vertical: true },
        {
          default: () => row.meals.map(meal => {
            return h(
              NTag,
              { type: 'info', round: true },
              { default: () => `${getMealTypeName(meal.meal_type)} (${meal.dish_list?.length || 0}道菜)` }
            )
          })
        }
      )
    }
  },
  {
    title: '状态',
    key: 'is_active',
    width: 100,
    align: 'center',
    render(row) {
      return row.is_active
        ? h(NTag, { type: 'success', round: true }, { default: () => '启用' })
        : h(NTag, { type: 'error', round: true }, { default: () => '停用' })
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 350,
    align: 'center',
    fixed: 'right',
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            type: 'info',
            style: 'margin-right: 8px;',
            onClick: () => handleViewDetail(row)
          },
          {
            default: () => '查看',
            icon: renderIcon('material-symbols:visibility-outline', { size: 16 })
          }
        ),
        withDirectives(
          h(
            NButton,
            {
              size: 'small',
              type: 'success',
              style: 'margin-right: 8px;',
              onClick: () => openOrderModal(row)
            },
            {
              default: () => '下单',
              icon: renderIcon('material-symbols:shopping-cart-outline', { size: 16 })
            }
          ),
          [[vPermission, 'post/api/v1/orders/create']]
        ),
        withDirectives(
          h(
            NButton,
            {
              size: 'small',
              type: 'primary',
              style: 'margin-right: 8px;',
              onClick: () => handleEdit(row)
            },
            {
              default: () => '编辑',
              icon: renderIcon('material-symbols:edit-outline', { size: 16 })
            }
          ),
          [[vPermission, 'post/api/v1/food-menu/update']]
        ),
        h(
          NPopconfirm,
          {
            onPositiveClick: () => handleDelete({ id: row.id }, false),
            onNegativeClick: () => {},
          },
          {
            trigger: () =>
              withDirectives(
                h(
                  NButton,
                  {
                    size: 'small',
                    type: 'error',
                    style: 'margin-right: 8px;',
                  },
                  {
                    default: () => '删除',
                    icon: renderIcon('material-symbols:delete-outline', { size: 16 }),
                  }
                ),
                [[vPermission, 'delete/api/v1/food-menu/delete']]
              ),
            default: () => h('div', {}, '确定删除该食谱吗?'),
          }
        )
      ]
    }
  }
]



// 初始化数据
onMounted(() => {
  fetchDishOptions()
  $table.value?.handleSearch()
})
</script>

<template>
  <CommonPage show-footer title="每周食谱">
    <template #action>
      <NSpace>
        <NButton
          v-permission="'post/api/v1/food-menu/create'"
          type="primary"
          @click="handleAdd"
        >
          <template #icon>
            <TheIcon icon="material-symbols:add" :size="18" class="mr-5" />
          </template>
          新增食谱
        </NButton>
      </NSpace>
    </template>

    <CrudTable
      ref="$table"
      v-model:query-items="queryItems"
      :columns="columns"
      :get-data="api.getRecipeList"
    >
      <template #queryBar>
        <QueryBarItem label="日期" :label-width="70">
          <NDatePicker
            style="width: 150px;"
            v-model:value="queryItems.recipe_date"
            type="date"
            clearable
            placeholder="请选择日期"
            @update:value="$table?.handleSearch()"
          />
        </QueryBarItem>
      </template>
    </CrudTable>

    <!-- 新建/编辑食谱弹窗 -->
    <CrudModal
      v-model:visible="modalVisible"
      :title="modalTitle"
      :loading="modalLoading"
      @save="handleSave"
      style="width: 900px"
    >
      <NForm
        ref="modalFormRef"
        label-placement="left"
        label-align="left"
        :label-width="100"
        :model="modalForm"
        :disabled="modalAction === 'view'"
      >
        <NFormItem
          label="日期"
          path="recipeDate"
          :rule="{
            required: true,
            message: '请选择日期',
            trigger: ['change', 'blur'],
          }"
        >
          <NDatePicker
            v-model:value="modalForm.recipeDate"
            type="date"
            placeholder="请选择日期"
          />
        </NFormItem>

        <NFormItem label="食谱名称" path="recipeName">
          <NInput
            v-model:value="modalForm.recipeName"
            placeholder="请输入食谱名称（可选）"
          />
        </NFormItem>

        <NFormItem label="餐次配置">
          <div class="meals-config-container">
            <div class="meals-container">
              <div v-for="(meal, mealIndex) in modalForm.meals" :key="mealIndex" class="meal-item">
                <NCard :title="`餐次 ${mealIndex + 1}`" size="small">
                  <template #header-extra>
                    <NButton
                      type="error"
                      circle
                      size="small"
                      @click="removeMeal(mealIndex)"
                      v-if="modalForm.meals.length > 1"
                    >
                      <template #icon>
                        <TheIcon icon="material-symbols:delete-outline" :size="16" />
                      </template>
                    </NButton>
                  </template>

                  <NFormItem label="餐次类型" :show-label="false">
                    <NSelect
                      v-model:value="meal.meal_type"
                      :options="mealTypeOptions"
                      placeholder="请选择餐次类型"
                      style="width: 200px; margin-bottom: 12px;"
                    />
                  </NFormItem>

                  <NFormItem label="菜品列表" :show-label="false">
                    <div class="dish-list-container">
                      <div class="dish-list">
                        <div v-for="(dish, dishIndex) in meal.dish_list" :key="dishIndex" class="dish-item">
                          <div class="dish-row">
                            <NSelect
                              v-model:value="dish.dish_id"
                              filterable
                              :options="dishOptions"
                              placeholder="请选择菜品"
                              @update:value="(value) => updateDishInfo(mealIndex, dishIndex, value)"
                              class="dish-select"
                            />
                            <NButton
                              type="error"
                              circle
                              size="small"
                              @click="removeDishFromMeal(mealIndex, dishIndex)"
                              class="remove-btn"
                            >
                              <template #icon>
                                <TheIcon icon="material-symbols:delete-outline" :size="16" />
                              </template>
                            </NButton>
                          </div>
                          <!-- 显示菜品详情 -->
                          <div v-if="dish.ingredients && dish.ingredients.length > 0" class="dish-details">
                            <h4>菜品食材：</h4>
                            <div class="ingredients-list">
                              <NTag
                                v-for="ingredient in dish.ingredients"
                                :key="ingredient.food_stuff_id"
                                type="info"
                                size="small"
                                style="margin: 2px;"
                              >
                                {{ ingredient.food_stuff_name }}: {{ ingredient.quantity }}{{ ingredient.unit_name }}
                              </NTag>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="add-dish-btn-container">
                        <NButton
                          type="primary"
                          @click="addDishToMeal(mealIndex)"
                          class="add-dish-btn"
                          size="small"
                          block
                        >
                          <template #icon>
                            <TheIcon icon="material-symbols:add" :size="16" />
                          </template>
                          添加菜品
                        </NButton>
                      </div>
                    </div>
                  </NFormItem>
                </NCard>
              </div>
            </div>
            <div class="add-meal-btn-container">
              <NButton
                type="primary"
                @click="addMeal"
                class="add-meal-btn"
                dashed
                block
              >
                <template #icon>
                  <TheIcon icon="material-symbols:add" :size="16" />
                </template>
                添加餐次
              </NButton>
            </div>
          </div>
        </NFormItem>
      </NForm>
    </CrudModal>

    <!-- 下单弹窗 -->
    <NModal
      v-model:show="showOrderModal"
      preset="card"
      title="生成订单"
      style="width: 800px"
      :mask-closable="true"
      :bordered="false"
      :loading="orderLoading"
    >
      <div v-if="currentMenu" style="padding: 16px 0">
        <NSpace vertical>
          <NCard title="食谱信息" size="small">
            <NSpace vertical>
              <div>
                <strong>日期：</strong>
                {{ currentMenu.recipe_date ? formatDateForDisplay(currentMenu.recipe_date) : '-' }}
              </div>
              <div>
                <strong>食谱名称：</strong>
                {{ currentMenu.recipe_name || '无' }}
              </div>
              <div>
                <strong>餐次：</strong>
                <NSpace>
                  <NTag
                    v-for="meal in currentMenu.meals"
                    :key="meal.meal_type"
                    type="info"
                    size="small"
                  >
                    {{ mealTypeOptions.find(item => item.value === meal.meal_type)?.label || `餐次${meal.meal_type}` }}
                  </NTag>
                </NSpace>
              </div>
            </NSpace>
          </NCard>

          <NCard title="学生信息" size="small">
            <NStatistic label="学生总数" :value="totalStudentCount" />
          </NCard>

          <NCard title="食材需求与库存" size="small" style="width: 100%">
            <NDataTable
              :columns="[
                { title: '食材名称', key: 'food_stuff_name', align: 'center' },
                { title: '单位数量', key: 'food_stuff_count', align: 'center' },
                { title: '单位', key: 'food_stuff_unit', align: 'center' },
                {
                  title: '需要数量',
                  key: 'required_amount',
                  align: 'center',
                  render: (row) => {
                    const amount = calculateRequiredAmount(row)
                    return h('span', `${amount} ${row.food_stuff_unit}`)
                  }
                },
                {
                  title: '当前库存',
                  key: 'store_amount',
                  align: 'center',
                  render: (row) => {
                    const amount = getStoreAmount(row.food_stuff_id)
                    return h('span', `${amount} ${row.food_stuff_unit}`)
                  }
                },
                {
                  title: '状态',
                  key: 'status',
                  align: 'center',
                  render: (row) => {
                    const required = calculateRequiredAmount(row)
                    const store = getStoreAmount(row.food_stuff_id)
                    const isEnough = store >= required

                    return h(
                      NTag,
                      { type: isEnough ? 'success' : 'error' },
                      { default: () => isEnough ? '库存充足' : '库存不足' }
                    )
                  }
                },
                {
                  title: '下单数量',
                  key: 'order_quantity',
                  align: 'center',
                  render: (row) => {
                    return h(NInputNumber, {
                      value: orderQuantities[row.food_stuff_id] || 0,
                      min: 0,
                      precision: 2,
                      step: 0.5,
                      style: 'width: 100px',
                      onUpdateValue: (value) => {
                        orderQuantities[row.food_stuff_id] = value
                      }
                    })
                  }
                }
              ]"
              :data="currentMenu.food_stuff_list"
              :bordered="false"
              size="small"
            />
          </NCard>
        </NSpace>
      </div>

      <template #footer>
        <div style="display: flex; justify-content: flex-end">
          <NSpace>
            <NButton @click="closeOrderModal">取消</NButton>
            <NPopconfirm
              positive-text="确认"
              negative-text="取消"
              @positive-click="submitOrder"
              placement="top"
            >
              <template #trigger>
                <NButton type="primary" :disabled="!currentMenu || !currentMenu.food_stuff_list || !currentMenu.food_stuff_list.length">确认下单</NButton>
              </template>
              确认要提交这个订单吗？
            </NPopconfirm>
          </NSpace>
        </div>
      </template>
    </NModal>

    <!-- 查看详情弹窗 -->
    <NModal
      v-model:show="showDetailModal"
      preset="card"
      title="食谱详情"
      style="width: 800px"
      :mask-closable="true"
      :bordered="false"
    >
      <div v-if="currentRecipeDetail" style="padding: 16px 0">
        <NSpace vertical>
          <NCard title="基本信息" size="small">
            <NSpace vertical>
              <div>
                <strong>日期：</strong>
                {{ currentRecipeDetail.recipe_date ? formatDateForDisplay(currentRecipeDetail.recipe_date) : '-' }}
              </div>
              <div>
                <strong>食谱名称：</strong>
                {{ currentRecipeDetail.recipe_name || '无' }}
              </div>
              <div>
                <strong>状态：</strong>
                <NTag :type="currentRecipeDetail.is_active ? 'success' : 'error'">
                  {{ currentRecipeDetail.is_active ? '启用' : '停用' }}
                </NTag>
              </div>
            </NSpace>
          </NCard>

          <NCard title="餐次详情" size="small">
            <NCollapse v-if="currentRecipeDetail.meals && currentRecipeDetail.meals.length > 0">
              <NCollapseItem
                v-for="(meal, index) in currentRecipeDetail.meals"
                :key="index"
                :title="`${getMealTypeName(meal.meal_type)} (${meal.dish_list?.length || 0}道菜)`"
              >
                <div v-if="meal.dish_list && meal.dish_list.length > 0">
                  <div v-for="(dish, dishIndex) in meal.dish_list" :key="dishIndex" class="detail-dish-item">
                    <h4>{{ dish.dish_name }}</h4>
                    <div v-if="dish.ingredients && dish.ingredients.length > 0" class="ingredients-list">
                      <NTag
                        v-for="ingredient in dish.ingredients"
                        :key="ingredient.food_stuff_id"
                        type="info"
                        size="small"
                        style="margin: 2px;"
                      >
                        {{ ingredient.food_stuff_name }}: {{ ingredient.quantity }}{{ ingredient.unit_name }}
                      </NTag>
                    </div>
                  </div>
                </div>
                <div v-else>
                  <span style="color: #999;">该餐次暂无菜品</span>
                </div>
              </NCollapseItem>
            </NCollapse>
            <div v-else>
              <span style="color: #999;">暂无餐次信息</span>
            </div>
          </NCard>
        </NSpace>
      </div>

      <template #footer>
        <div style="display: flex; justify-content: flex-end">
          <NButton @click="closeDetailModal">关闭</NButton>
        </div>
      </template>
    </NModal>
  </CommonPage>
</template>

<style scoped>
/* 餐次配置容器 */
.meals-config-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.meals-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 16px;
}

.meal-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

/* 菜品列表容器 */
.dish-list-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.dish-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 12px;
}

.dish-item {
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 12px;
  background-color: #fafafa;
}

.dish-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.dish-select {
  flex: 1;
}

.remove-btn {
  margin-left: 10px;
}

.dish-details {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #e0e0e0;
}

.dish-details h4 {
  margin: 0 0 6px 0;
  font-size: 13px;
  color: #666;
}

.ingredients-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

/* 添加按钮容器 */
.add-dish-btn-container {
  display: flex;
  width: 100%;
  margin-top: 12px;
}

.add-dish-btn {
  width: 100%;
}

.add-meal-btn-container {
  display: flex;
  width: 100%;
  margin-top: 16px;
}

.add-meal-btn {
  width: 100%;
}

/* 详情页面样式 */
.detail-dish-item {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.detail-dish-item h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #333;
}

.detail-dish-item:last-child {
  margin-bottom: 0;
}
</style>